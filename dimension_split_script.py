#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频分析结果处理脚本
处理video_analysis_realtime_20250723_135640.xlsx文件中的七维度分析数据
提供数据清理、统计分析和结果导出功能
"""

import pandas as pd
import json
import re
from datetime import datetime
import os
import numpy as np

def clean_text_content(text):
    """清理文本内容，移除多余的空白字符和特殊标记"""
    if not isinstance(text, str):
        return ""

    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text.strip())

    # 移除代码标记
    text = re.sub(r'```[a-zA-Z]*\s*', '', text)
    text = re.sub(r'\s*```', '', text)

    # 移除多余的换行符
    text = re.sub(r'\n+', '\n', text)

    return text.strip()

def extract_dimension_content(text, dimension_name, dimension_number):
    """从完整分析文本中提取指定维度的内容"""
    if not isinstance(text, str) or not text.strip():
        return ""
    
    try:
        # 首先尝试新的JSON格式解析
        json_content = extract_json_dimension_content_v2(text, dimension_name)
        if json_content:
            return json_content
            
        # 然后尝试旧的JSON格式解析
        json_content = extract_json_dimension_content(text, dimension_name)
        if json_content:
            return json_content
        
        # 如果JSON解析失败，尝试原有的代码格式
        patterns = [
            # 标准格式: ### 1. **人**
            rf'###\s*{dimension_number}\.\s*\*\*{re.escape(dimension_name)}\*\*\s*(.*?)(?=###\s*\d+\.\s*\*\*|\Z)',
            # 灵活格式: ### 1.**人** 或 ###1. **人**
            rf'###?\s*{dimension_number}\s*\.?\s*\*?\*?{re.escape(dimension_name)}\*?\*?\s*(.*?)(?=###?\s*\d+|$)',
            # 简单格式: 人: 或 **人**
            rf'{re.escape(dimension_name)}\s*[:：]\s*(.*?)(?=\n\s*[人事地物情组织文化]|$)',
            # 备用格式: **人** (独立行)
            rf'\*\*{re.escape(dimension_name)}\*\*\s*(.*?)(?=\*\*[人事地物情组织文化]\*\*|$)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.DOTALL | re.IGNORECASE)
            if match:
                content = match.group(1).strip()
                if content:
                    # 清理提取的内容
                    content = re.sub(r'\n+', '\n', content)  # 合并多个换行符
                    content = re.sub(r'^\s*-\s*', '', content, flags=re.MULTILINE)  # 移除行首的破折号
                    return content.strip()
        
        return ""
        
    except Exception as e:
        print(f"⚠️  提取维度 '{dimension_name}' 内容时出错: {e}")
        return ""


def extract_json_dimension_content(text, dimension_name):
    """从新的JSON格式的完整分析中提取维度内容"""
    try:
        # 维度名称映射
        dimension_mapping = {
            '人': 'persons',
            '事': 'events',
            '地': 'locations',
            '物': 'objects',
            '情': 'emotions',
            '组织': 'organizations',
            '文化': 'cultures'
        }

        json_key = dimension_mapping.get(dimension_name)
        if not json_key:
            return ""

        # 尝试解析为Python字面量
        import ast
        data = ast.literal_eval(text.strip())

        # 查找对应的维度数据
        dimension_data = None
        for item in data:
            if isinstance(item, (tuple, list)) and len(item) == 2 and item[0] == json_key:
                dimension_data = item[1]
                break

        # 如果维度数据是空列表，返回"null"
        if isinstance(dimension_data, list) and len(dimension_data) == 0:
            return "null"

        if not dimension_data:
            return ""

        # 格式化维度内容
        formatted_content = format_dimension_data_new(dimension_data, dimension_name)
        return formatted_content

    except (SyntaxError, ValueError) as e:
        print(f"⚠️  解析维度 '{dimension_name}' 时出错: {e}")
        return ""
    except Exception as e:
        print(f"⚠️  处理维度 '{dimension_name}' 时出错: {e}")
        return ""


def format_dimension_data_new(data, dimension_name):
    """格式化新的维度数据为可读文本"""
    if not data:
        return "null"

    try:
        formatted_lines = []

        for i, item in enumerate(data):
            if not isinstance(item, dict):
                continue

            item_lines = []

            if dimension_name == '人':
                # 处理人物维度
                if '外显特征' in item:
                    traits = item['外显特征']
                    for key, value in traits.items():
                        if value is not None:  # 只显示非None的值
                            item_lines.append(f"{key}: {value}")
                        else:
                            item_lines.append(f"{key}: null")

            elif dimension_name == '事':
                # 处理事件维度
                if '事件类型' in item:
                    item_lines.append(f"事件类型: {item['事件类型']}")
                if '时间' in item:
                    time_info = item['时间']
                    for key, value in time_info.items():
                        if value is not None:
                            item_lines.append(f"{key}: {value}")
                        else:
                            item_lines.append(f"{key}: null")
                if '关键内容/描述/结果/影响' in item:
                    content_info = item['关键内容/描述/结果/影响']
                    for key, value in content_info.items():
                        if value is not None:
                            item_lines.append(f"{key}: {value}")
                        else:
                            item_lines.append(f"{key}: null")

            elif dimension_name == '地':
                # 处理地点维度
                for key, value in item.items():
                    if value is not None:
                        item_lines.append(f"{key}: {value}")
                    else:
                        item_lines.append(f"{key}: null")

            elif dimension_name == '物':
                # 处理物品维度
                for key, value in item.items():
                    if value is not None:
                        if isinstance(value, dict):
                            # 处理嵌套字典（如使用信息）
                            sub_lines = []
                            for sub_key, sub_value in value.items():
                                if sub_value is not None:
                                    sub_lines.append(f"{sub_key}: {sub_value}")
                                else:
                                    sub_lines.append(f"{sub_key}: null")
                            if sub_lines:
                                item_lines.append(f"{key}:")
                                item_lines.extend([f"  {line}" for line in sub_lines])
                        else:
                            item_lines.append(f"{key}: {value}")
                    else:
                        item_lines.append(f"{key}: null")

            elif dimension_name == '情':
                # 处理情感维度
                for key, value in item.items():
                    if value is not None:
                        item_lines.append(f"{key}: {value}")
                    else:
                        item_lines.append(f"{key}: null")

            elif dimension_name == '组织':
                # 处理组织维度
                for key, value in item.items():
                    if value is not None:
                        item_lines.append(f"{key}: {value}")
                    else:
                        item_lines.append(f"{key}: null")

            elif dimension_name == '文化':
                # 处理文化维度
                for key, value in item.items():
                    if value is not None:
                        item_lines.append(f"{key}: {value}")
                    else:
                        item_lines.append(f"{key}: null")

            if item_lines:
                if len(data) > 1:
                    formatted_lines.append(f"项目 {i + 1}:")
                formatted_lines.extend(item_lines)

        return '\n'.join(formatted_lines) if formatted_lines else "null"

    except Exception as e:
        print(f"⚠️  格式化维度数据时出错: {e}")
        return "null"

# 新增的函数：支持另一种JSON格式
def extract_json_dimension_content_v2(text, dimension_name):
    """从另一种JSON格式的完整分析中提取维度内容"""
    try:
        # 维度名称映射
        dimension_mapping = {
            '人': 'persons',
            '事': 'events',
            '地': 'locations',
            '物': 'objects',
            '情': 'emotions',
            '组织': 'organizations',
            '文化': 'cultures'
        }

        json_key = dimension_mapping.get(dimension_name)
        if not json_key:
            return ""

        # 尝试解析为JSON
        data = json.loads(text.strip())
        
        # 查找对应的维度数据
        dimension_data = data.get(json_key, [])
        
        # 如果维度数据是空列表，返回"null"
        if isinstance(dimension_data, list) and len(dimension_data) == 0:
            return "null"
            
        if not dimension_data:
            return ""
            
        # 格式化维度内容
        formatted_content = format_dimension_data_new_v2(dimension_data, dimension_name)
        return formatted_content
        
    except (json.JSONDecodeError, ValueError) as e:
        # 不打印错误信息，因为可能不是这种格式
        return ""
    except Exception as e:
        print(f"⚠️  处理维度 '{dimension_name}' 时出错: {e}")
        return ""

def format_dimension_data_new_v2(data, dimension_name):
    """格式化新的维度数据为可读文本 (v2版本)"""
    if not data:
        return "null"

    try:
        formatted_lines = []

        for i, item in enumerate(data):
            if not isinstance(item, dict):
                continue

            item_lines = []

            if dimension_name == '人':
                # 处理人物维度
                if '外显特征' in item:
                    traits = item['外显特征']
                    for key, value in traits.items():
                        if value is not None:  # 只显示非None的值
                            item_lines.append(f"{key}: {value}")
                        else:
                            item_lines.append(f"{key}: null")

            elif dimension_name == '事':
                # 处理事件维度
                if '事件类型' in item:
                    item_lines.append(f"事件类型: {item['事件类型']}")
                if '时间' in item:
                    time_info = item['时间']
                    for key, value in time_info.items():
                        if value is not None:
                            item_lines.append(f"{key}: {value}")
                        else:
                            item_lines.append(f"{key}: null")
                if '关键内容/描述/结果/影响' in item:
                    content_info = item['关键内容/描述/结果/影响']
                    for key, value in content_info.items():
                        if value is not None:
                            item_lines.append(f"{key}: {value}")
                        else:
                            item_lines.append(f"{key}: null")

            elif dimension_name == '地':
                # 处理地点维度
                for key, value in item.items():
                    if value is not None:
                        item_lines.append(f"{key}: {value}")
                    else:
                        item_lines.append(f"{key}: null")

            elif dimension_name == '物':
                # 处理物品维度
                for key, value in item.items():
                    if value is not None:
                        if isinstance(value, dict):
                            # 处理嵌套字典（如使用信息）
                            sub_lines = []
                            for sub_key, sub_value in value.items():
                                if sub_value is not None:
                                    sub_lines.append(f"{sub_key}: {sub_value}")
                                else:
                                    sub_lines.append(f"{sub_key}: null")
                            if sub_lines:
                                item_lines.append(f"{key}:")
                                item_lines.extend([f"  {line}" for line in sub_lines])
                        else:
                            item_lines.append(f"{key}: {value}")
                    else:
                        item_lines.append(f"{key}: null")

            elif dimension_name == '情':
                # 处理情感维度
                for key, value in item.items():
                    if value is not None:
                        item_lines.append(f"{key}: {value}")
                    else:
                        item_lines.append(f"{key}: null")

            elif dimension_name == '组织':
                # 处理组织维度
                for key, value in item.items():
                    if value is not None:
                        item_lines.append(f"{key}: {value}")
                    else:
                        item_lines.append(f"{key}: null")

            elif dimension_name == '文化':
                # 处理文化维度
                for key, value in item.items():
                    if value is not None:
                        item_lines.append(f"{key}: {value}")
                    else:
                        item_lines.append(f"{key}: null")

            if item_lines:
                if len(data) > 1:
                    formatted_lines.append(f"项目 {i + 1}:")
                formatted_lines.extend(item_lines)

        return '\n'.join(formatted_lines) if formatted_lines else "null"

    except Exception as e:
        print(f"⚠️  格式化维度数据时出错: {e}")
        return "null"

def format_dimension_data(data, dimension_name):
    """格式化维度数据为可读文本"""
    if not data:
        return ""
    
    try:
        formatted_lines = []
        
        for i, item in enumerate(data):
            if not isinstance(item, dict):
                continue
                
            item_lines = []
            
            if dimension_name == '人':
                # 处理人物维度
                if '外显特征' in item:
                    traits = item['外显特征']
                    for key, value in traits.items():
                        if value:  # 包含"无"的值也显示
                            item_lines.append(f"{key}: {value}")
                            
            elif dimension_name == '事':
                # 处理事件维度
                if '事件类型' in item:
                    item_lines.append(f"事件类型: {item['事件类型']}")
                if '时间' in item:
                    time_info = item['时间']
                    for key, value in time_info.items():
                        if value:  # 包含"无"的值也显示
                            item_lines.append(f"{key}: {value}")
                if '关键内容/描述/结果/影响' in item:
                    content_info = item['关键内容/描述/结果/影响']
                    for key, value in content_info.items():
                        if value:  # 包含"无"的值也显示
                            item_lines.append(f"{key}: {value}")
                            
            elif dimension_name == '地':
                # 处理地点维度
                for key, value in item.items():
                    if value:  # 包含"无"的值也显示
                        item_lines.append(f"{key}: {value}")
                        
            elif dimension_name == '物':
                # 处理物品维度
                if '类型' in item:
                    item_lines.append(f"类型: {item['类型']}")
                if '作用' in item:
                    item_lines.append(f"作用: {item['作用']}")
                if '使用信息' in item:
                    usage_info = item['使用信息']
                    for key, value in usage_info.items():
                        if value:  # 包含"无"的值也显示
                            item_lines.append(f"{key}: {value}")
                            
            elif dimension_name == '情':
                # 处理情感维度
                for key, value in item.items():
                    if value:  # 包含"无"的值也显示
                        item_lines.append(f"{key}: {value}")
                        
            elif dimension_name == '组织':
                # 处理组织维度
                for key, value in item.items():
                    if value:  # 包含"无"的值也显示
                        item_lines.append(f"{key}: {value}")
                        
            elif dimension_name == '文化':
                # 处理文化维度
                for key, value in item.items():
                    if value:  # 包含"无"的值也显示
                        item_lines.append(f"{key}: {value}")
            
            if item_lines:
                if len(data) > 1:
                    formatted_lines.append(f"项目 {i+1}:")
                formatted_lines.extend([f"  {line}" for line in item_lines])
        
        return '\n'.join(formatted_lines) if formatted_lines else ""
        
    except Exception as e:
        print(f"⚠️  格式化维度数据时出错: {e}")
        return ""

def parse_complete_analysis(complete_text):
    """解析完整分析文本，提取所有七个维度的内容"""
    if not isinstance(complete_text, str) or not complete_text.strip():
        return {}
    
    dimensions = {
        '人': 1,
        '事': 2, 
        '地': 3,
        '物': 4,
        '情': 5,
        '组织': 6,
        '文化': 7
    }
    
    result = {}
    
    try:
        for dim_name, dim_number in dimensions.items():
            content = extract_dimension_content(complete_text, dim_name, dim_number)
            if content:
                result[dim_name] = content
            
        return result
        
    except Exception as e:
        print(f"⚠️  解析完整分析时出错: {e}")
        return {}

def analyze_data_quality(df):
    """分析数据质量"""
    print("📊 数据质量分析:")
    print(f"  总行数: {len(df)}")

    # 分析各维度的数据完整性
    dimensions = ['人', '事', '地', '物', '情', '组织', '文化']

    for dim in dimensions:
        if dim in df.columns:
            # 统计非空数据
            non_null = df[dim].notna().sum()
            non_empty = (df[dim].notna() & (df[dim] != '') & (df[dim] != 'NaN')).sum()

            print(f"  {dim}: {non_empty}/{len(df)} ({non_empty/len(df)*100:.1f}%) 有效数据")

    # 分析分析状态
    if '分析状态' in df.columns:
        status_counts = df['分析状态'].value_counts()
        print(f"\n📈 分析状态统计:")
        for status, count in status_counts.items():
            print(f"  {status}: {count} ({count/len(df)*100:.1f}%)")

    # 分析错误信息
    if '错误信息' in df.columns:
        error_count = df['错误信息'].notna().sum()
        if error_count > 0:
            print(f"\n⚠️  错误统计: {error_count} 行有错误信息")

    return {
        'total_rows': len(df),
        'dimension_stats': {dim: (df[dim].notna() & (df[dim] != '') & (df[dim] != 'NaN')).sum()
                           for dim in dimensions if dim in df.columns}
    }

def clean_dimension_data(text):
    """清理维度数据"""
    if pd.isna(text) or text == '' or str(text).lower() == 'nan':
        return ""

    text = str(text)
    text = clean_text_content(text)

    # 移除常见的无效内容
    if text.lower() in ['无', 'none', 'null', '无内容', '未检测到']:
        return ""

    return text

def extract_keywords_from_dimension(text, max_keywords=5):
    """从维度文本中提取关键词"""
    if not text or text == "":
        return []

    # 简单的关键词提取（可以后续优化为更复杂的NLP方法）
    # 移除标点符号，分割文本
    words = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z]+', text)

    # 过滤掉常见的停用词
    stop_words = {'的', '了', '在', '是', '有', '和', '与', '或', '但', '而', '等', '及'}
    keywords = [word for word in words if len(word) > 1 and word not in stop_words]

    # 返回前N个关键词
    return keywords[:max_keywords]

def filter_successful_analyses(df):
    """筛选分析成功的数据"""
    if '分析状态' in df.columns:
        successful_df = df[df['分析状态'] == '成功'].copy()
        print(f"📋 筛选出 {len(successful_df)} 行分析成功的数据")
        return successful_df
    else:
        print("⚠️  未找到'分析状态'列，返回所有数据")
        return df.copy()

def split_dimensions_batch(df):
    """批量处理DataFrame，将完整分析内容拆分到各维度列"""
    if '完整分析' not in df.columns:
        print("❌ 错误：未找到'完整分析'列")
        return df, {
            'total_processed': 0,
            'successful_splits': 0,
            'skipped_rows': 0,
            'error_count': 0,
            'dimension_stats': {}
        }
    
    dimensions = ['人', '事', '地', '物', '情', '组织', '文化']
    
    # 确保所有维度列都存在
    for dim in dimensions:
        if dim not in df.columns:
            df[dim] = ""
    
    # 初始化统计信息
    stats = {
        'total_processed': len(df),
        'successful_splits': 0,
        'skipped_rows': 0,
        'error_count': 0,
        'dimension_stats': {dim: 0 for dim in dimensions}
    }
    
    print(f"🚀 开始批量拆分维度内容，共 {len(df)} 行数据")
    
    # 处理每一行
    for index, row in df.iterrows():
        try:
            complete_analysis = row.get('完整分析', '')
            
            # 不再跳过空的完整分析，而是处理所有行
            # 解析完整分析内容
            parsed_dimensions = parse_complete_analysis(str(complete_analysis))
            
            # 将解析结果填充到对应维度列（即使为空）
            has_content = False
            for dim_name in dimensions:
                if dim_name in parsed_dimensions and parsed_dimensions[dim_name].strip():
                    df.at[index, dim_name] = parsed_dimensions[dim_name].strip()
                    stats['dimension_stats'][dim_name] += 1
                    has_content = True
                else:
                    # 确保空值也设置为空字符串
                    df.at[index, dim_name] = ""
            
            # 修复：无论是否有内容，都应增加successful_splits计数
            # 因为我们处理了这一行（进行了拆分尝试）
            stats['successful_splits'] += 1
            
            # 显示进度
            if (index + 1) % 20 == 0 or (index + 1) == len(df):
                progress = ((index + 1) / len(df)) * 100
                print(f"📊 处理进度: {progress:.1f}% ({index + 1}/{len(df)})")
                
        except Exception as e:
            print(f"⚠️  处理第 {index + 1} 行时出错: {e}")
            stats['error_count'] += 1
            # 出错时确保维度列为空
            for dim in dimensions:
                df.at[index, dim] = ""
            continue
    
    print(f"✅ 批量拆分完成！")
    print(f"   总处理行数: {stats['total_processed']}")
    print(f"   成功拆分: {stats['successful_splits']}")
    print(f"   跳过行数: {stats['skipped_rows']}")
    print(f"   错误行数: {stats['error_count']}")
    
    # 显示各维度统计
    print(f"\n📈 各维度拆分统计:")
    for dim, count in stats['dimension_stats'].items():
        print(f"   {dim}: {count} 条")
    
    return df, stats

def generate_dimension_summary(df):
    """生成各维度的内容摘要"""
    dimensions = ['人', '事', '地', '物', '情', '组织', '文化']
    summary = {}

    for dim in dimensions:
        if dim in df.columns:
            # 清理数据
            clean_data = df[dim].apply(clean_dimension_data)
            valid_data = clean_data[clean_data != '']

            if len(valid_data) > 0:
                # 提取所有关键词
                all_keywords = []
                for text in valid_data:
                    keywords = extract_keywords_from_dimension(text)
                    all_keywords.extend(keywords)

                # 统计关键词频率
                from collections import Counter
                keyword_freq = Counter(all_keywords)
                top_keywords = keyword_freq.most_common(10)

                summary[dim] = {
                    'total_entries': len(valid_data),
                    'top_keywords': top_keywords,
                    'sample_content': valid_data.iloc[0][:100] + "..." if len(valid_data.iloc[0]) > 100 else valid_data.iloc[0]
                }
            else:
                summary[dim] = {
                    'total_entries': 0,
                    'top_keywords': [],
                    'sample_content': ""
                }

    return summary

def export_cleaned_data(df, output_file):
    """导出清理后的数据"""
    try:
        # 清理所有维度数据
        dimensions = ['人', '事', '地', '物', '情', '组织', '文化']
        cleaned_df = df.copy()

        for dim in dimensions:
            if dim in cleaned_df.columns:
                cleaned_df[dim] = cleaned_df[dim].apply(clean_dimension_data)

        # 保存到Excel
        cleaned_df.to_excel(output_file, index=False, engine='openpyxl')
        print(f"✅ 清理后的数据已保存到: {output_file}")
        return True

    except Exception as e:
        print(f"❌ 导出失败: {e}")
        return False

def validate_dataframe_structure(df):
    """验证DataFrame结构是否符合要求"""
    required_columns = ['完整分析']
    dimension_columns = ['人', '事', '地', '物', '情', '组织', '文化']
    
    # 检查必需列
    missing_required = [col for col in required_columns if col not in df.columns]
    if missing_required:
        print(f"❌ 缺少必需列: {missing_required}")
        return False
    
    # 检查维度列，如果不存在则创建
    missing_dimensions = [col for col in dimension_columns if col not in df.columns]
    if missing_dimensions:
        print(f"⚠️  缺少维度列: {missing_dimensions}，将自动创建")
        for col in missing_dimensions:
            df[col] = ""
    
    # 检查数据量
    if len(df) == 0:
        print("❌ 数据文件为空")
        return False
    
    # 检查完整分析列的有效数据量
    valid_analysis_count = df['完整分析'].notna().sum()
    if valid_analysis_count == 0:
        print("❌ '完整分析'列中没有有效数据")
        return False
    
    print(f"✅ 数据结构验证通过，共 {len(df)} 行数据，其中 {valid_analysis_count} 行有完整分析内容")
    return True

def process_dimension_splitting():
    """处理维度拆分的主函数"""
    input_file = "data_res_72b_0731.xlsx"
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = f"维度拆分结果_{timestamp}.xlsx"

    print(f"🚀 开始处理维度拆分，输入文件: {input_file}")

    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"❌ 错误: 找不到输入文件 '{input_file}'")
        print(f"💡 建议: 请确认文件路径是否正确，文件是否存在")
        return None, None

    try:
        # 读取Excel文件
        print("📖 正在读取Excel文件...")
        df = pd.read_excel(input_file)
        print(f"✅ 成功读取文件，共 {len(df)} 行数据")

        # 显示文件结构
        print(f"📋 文件包含以下列: {list(df.columns)}")

        # 验证数据结构
        if not validate_dataframe_structure(df):
            return None, None

        # 执行维度拆分
        print(f"\n🔄 开始执行维度拆分...")
        processed_df, split_stats = split_dimensions_batch(df)

        # 验证处理结果
        if split_stats['successful_splits'] == 0:
            print("⚠️  警告: 没有成功拆分任何数据，请检查完整分析内容格式")

        # 导出处理后的数据
        print(f"\n💾 正在导出拆分后的数据...")
        export_success = export_cleaned_data(processed_df, output_file)

        if export_success:
            print(f"\n🎉 维度拆分完成！")
            print(f"📊 原始数据: {len(df)} 行")
            print(f"📊 成功拆分: {split_stats['successful_splits']} 行")
            print(f"📊 跳过行数: {split_stats['skipped_rows']} 行")
            print(f"📊 错误行数: {split_stats['error_count']} 行")
            print(f"📄 结果文件: {output_file}")
            
            # 显示拆分效果统计
            success_rate = (split_stats['successful_splits'] / len(df)) * 100 if len(df) > 0 else 0
            print(f"📈 拆分成功率: {success_rate:.1f}%")

        return processed_df, split_stats

    except FileNotFoundError:
        print(f"❌ 文件未找到: {input_file}")
        return None, None
    except pd.errors.EmptyDataError:
        print("❌ Excel文件为空或格式错误")
        return None, None
    except pd.errors.ParserError as e:
        print(f"❌ Excel文件解析错误: {e}")
        return None, None
    except PermissionError:
        print(f"❌ 文件权限错误，无法读取文件: {input_file}")
        return None, None
    except Exception as e:
        print(f"❌ 处理失败: {type(e).__name__}: {e}")
        print("💡 建议: 请检查文件格式是否正确，或联系技术支持")
        return None, None

def create_dimension_report(df, summary):
    """创建维度分析报告"""
    print("\n" + "="*80)
    print("📊 七维度分析报告")
    print("="*80)

    dimensions = ['人', '事', '地', '物', '情', '组织', '文化']

    for dim in dimensions:
        if dim in summary:
            info = summary[dim]
            print(f"\n🔍 【{dim}】维度分析:")
            print(f"  有效条目数: {info['total_entries']}")

            if info['top_keywords']:
                print(f"  高频关键词:")
                for keyword, freq in info['top_keywords'][:5]:
                    print(f"    - {keyword}: {freq}次")

            if info['sample_content']:
                print(f"  示例内容: {info['sample_content']}")
            else:
                print(f"  示例内容: 无有效内容")

    print("\n" + "="*80)

def process_video_analysis_data():
    """处理视频分析数据的主函数"""
    input_file = "video_analysis_realtime_20250723_164703.xlsx"
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = f"视频分析处理结果_{timestamp}.xlsx"

    print(f"🚀 开始处理视频分析数据文件: {input_file}")

    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"❌ 错误: 找不到输入文件 '{input_file}'")
        print(f"💡 建议: 请确认文件路径是否正确，文件是否存在")
        return

    try:
        # 读取Excel文件
        df = pd.read_excel(input_file)
        print(f"✅ 成功读取文件，共 {len(df)} 行数据")

        # 显示文件结构
        print(f"📋 文件包含以下列: {list(df.columns)}")

        # 数据质量分析
        quality_stats = analyze_data_quality(df)

        # 筛选分析成功的数据
        successful_df = filter_successful_analyses(df)

        # 生成维度摘要
        print(f"\n🔍 正在生成维度内容摘要...")
        summary = generate_dimension_summary(successful_df)

        # 创建分析报告
        create_dimension_report(successful_df, summary)

        # 导出清理后的数据
        print(f"\n💾 正在导出清理后的数据...")
        export_success = export_cleaned_data(successful_df, output_file)

        if export_success:
            print(f"\n🎉 处理完成！")
            print(f"📊 原始数据: {len(df)} 行")
            print(f"📊 成功分析: {len(successful_df)} 行")
            print(f"📄 结果文件: {output_file}")

        return successful_df, summary

    except Exception as e:
        print(f"❌ 处理失败: {type(e).__name__}: {e}")
        return None, None

def search_videos_by_dimension(df, dimension, keyword):
    """根据维度和关键词搜索视频"""
    if dimension not in df.columns:
        print(f"❌ 维度 '{dimension}' 不存在")
        return pd.DataFrame()

    # 清理数据并搜索
    mask = df[dimension].apply(lambda x: keyword.lower() in str(x).lower() if pd.notna(x) else False)
    results = df[mask].copy()

    print(f"🔍 在'{dimension}'维度中搜索'{keyword}'，找到 {len(results)} 个结果")
    return results

def get_dimension_statistics(df):
    """获取各维度的详细统计信息"""
    dimensions = ['人', '事', '地', '物', '情', '组织', '文化']
    stats = {}

    for dim in dimensions:
        if dim in df.columns:
            # 清理数据
            clean_data = df[dim].apply(clean_dimension_data)
            valid_data = clean_data[clean_data != '']

            # 计算统计信息
            total_chars = sum(len(text) for text in valid_data)
            avg_length = total_chars / len(valid_data) if len(valid_data) > 0 else 0

            stats[dim] = {
                'total_entries': len(df),
                'valid_entries': len(valid_data),
                'empty_entries': len(df) - len(valid_data),
                'completion_rate': len(valid_data) / len(df) * 100 if len(df) > 0 else 0,
                'avg_content_length': avg_length,
                'total_content_length': total_chars
            }

    return stats

def create_interactive_menu():
    """创建交互式菜单"""
    print("\n" + "="*60)
    print("🎯 视频分析数据处理工具")
    print("="*60)
    print("1. 📊 完整数据分析和处理")
    print("2. 🔍 按维度搜索视频")
    print("3. 📈 查看详细统计信息")
    print("4. 💾 导出特定数据")
    print("5. 🧹 数据清理和验证")
    print("6. 🔄 拆分完整分析到维度列")
    print("7. ❌ 退出")
    print("="*60)

    while True:
        try:
            choice = input("\n请选择操作 (1-7): ").strip()
            if choice in ['1', '2', '3', '4', '5', '6', '7']:
                return int(choice)
            else:
                print("❌ 请输入有效的选项 (1-7)")
        except KeyboardInterrupt:
            print("\n👋 程序已退出")
            return 7
        except Exception:
            print("❌ 输入错误，请重试")

def interactive_search(df):
    """交互式搜索功能"""
    dimensions = ['人', '事', '地', '物', '情', '组织', '文化']

    print("\n🔍 维度搜索功能")
    print("可用维度:", ", ".join(dimensions))

    dimension = input("请输入要搜索的维度: ").strip()
    if dimension not in dimensions:
        print(f"❌ 维度 '{dimension}' 不存在")
        return

    keyword = input("请输入搜索关键词: ").strip()
    if not keyword:
        print("❌ 关键词不能为空")
        return

    results = search_videos_by_dimension(df, dimension, keyword)

    if len(results) > 0:
        print(f"\n📋 搜索结果 (共 {len(results)} 条):")
        for idx, row in results.head(10).iterrows():
            print(f"\n{idx+1}. {row.get('视频名称', 'N/A')}")
            content = clean_dimension_data(row[dimension])
            if len(content) > 200:
                content = content[:200] + "..."
            print(f"   {dimension}: {content}")

        if len(results) > 10:
            print(f"\n... 还有 {len(results)-10} 条结果未显示")
    else:
        print("❌ 未找到匹配的结果")

def main():
    """主函数 - 提供交互式界面"""
    # 首先尝试加载数据
    input_file = "data_res_72b_0731.xlsx"

    if not os.path.exists(input_file):
        print(f"❌ 错误: 找不到输入文件 '{input_file}'")
        print(f"💡 请确保文件在当前目录下")
        return

    try:
        df = pd.read_excel(input_file)
        print(f"✅ 成功加载数据文件，共 {len(df)} 行数据")
    except Exception as e:
        print(f"❌ 加载数据失败: {e}")
        return

    # 交互式菜单循环
    while True:
        choice = create_interactive_menu()

        if choice == 1:
            # 完整数据分析和处理
            print("\n🚀 开始完整数据分析...")
            result_df, summary = process_video_analysis_data()
            if result_df is not None:
                print("✅ 分析完成！")

        elif choice == 2:
            # 按维度搜索视频
            interactive_search(df)

        elif choice == 3:
            # 查看详细统计信息
            print("\n📈 生成详细统计信息...")
            stats = get_dimension_statistics(df)
            print("\n📊 各维度详细统计:")
            for dim, stat in stats.items():
                print(f"\n🔍 【{dim}】维度:")
                print(f"  总条目: {stat['total_entries']}")
                print(f"  有效条目: {stat['valid_entries']}")
                print(f"  空白条目: {stat['empty_entries']}")
                print(f"  完成率: {stat['completion_rate']:.1f}%")
                print(f"  平均内容长度: {stat['avg_content_length']:.1f} 字符")

        elif choice == 4:
            # 导出特定数据
            print("\n💾 导出功能")
            successful_df = filter_successful_analyses(df)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"导出数据_{timestamp}.xlsx"
            if export_cleaned_data(successful_df, output_file):
                print(f"✅ 数据已导出到: {output_file}")

        elif choice == 5:
            # 数据清理和验证
            print("\n🧹 数据清理和验证")
            analyze_data_quality(df)

        elif choice == 6:
            # 拆分完整分析到维度列
            print("\n� 开始拆用分完整分析到维度列...")
            result_df, split_stats = process_dimension_splitting()
            if result_df is not None:
                print("✅ 维度拆分完成！")

        elif choice == 7:
            # 退出
            print("\n👋 感谢使用，再见！")
            break

if __name__ == "__main__":
    main()
