#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查看拆分结果
"""

import pandas as pd

def check_results():
    df = pd.read_excel('pre/拆分脚本/七维度拆分结果_20250718_095305.xlsx')
    
    print(f"结果文件形状: {df.shape}")
    print(f"列名: {df.columns.tolist()}")
    print()
    
    print("查看几行有内容的数据:")
    for i in [25, 30, 35, 40, 50]:
        if i < len(df):
            row = df.iloc[i]
            print(f"第{i+1}行 - {row['文件名']}:")
            for col in ['人', '事', '地', '物', '情', '组织', '文化']:
                if pd.notna(row[col]) and row[col] != '':
                    content = str(row[col])
                    if len(content) > 150:
                        content = content[:150] + "..."
                    print(f"  {col}: {content}")
            print()

if __name__ == "__main__":
    check_results()
